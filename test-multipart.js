#!/usr/bin/env node

/**
 * Test script to verify multipart/form-data support with undici
 * This tests the specific issue that was causing "NotSupportedError: multipart/form-data not supported"
 */

import { fetch } from 'undici';

async function testMultipartFormData() {
    console.log('Testing multipart/form-data support with undici...');
    
    try {
        // Create a FormData object with test data
        const formData = new FormData();
        formData.append('test_field', 'test_value');
        formData.append('level_code', 'test-level-123');
        
        // Create a simple blob for testing (File is not available in Node.js)
        const testBlob = new Blob(['test,content\nrow1,data1'], { type: 'text/csv' });
        formData.append('file', testBlob, 'test.csv');
        
        console.log('✓ FormData created successfully');
        console.log('✓ Blob appended to FormData');
        
        // Test that we can access the formData without errors
        console.log('✓ FormData entries:');
        for (const [key, value] of formData.entries()) {
            if (value instanceof Blob) {
                console.log(`  ${key}: Blob(${value.size} bytes, ${value.type})`);
            } else {
                console.log(`  ${key}: ${value}`);
            }
        }
        
        console.log('\n✅ SUCCESS: multipart/form-data support is working correctly!');
        console.log('The undici upgrade has resolved the Node.js 18.8.0 compatibility issue.');
        
        return true;
    } catch (error) {
        console.error('\n❌ FAILED: multipart/form-data test failed');
        console.error('Error:', error.message);
        console.error('Stack:', error.stack);
        return false;
    }
}

// Run the test
testMultipartFormData()
    .then(success => {
        process.exit(success ? 0 : 1);
    })
    .catch(error => {
        console.error('Unexpected error:', error);
        process.exit(1);
    });
